<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Game;
use App\Models\Category;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Support\Str;

class GameSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('is_admin', true)->first();
        $categories = Category::all();
        $tags = Tag::all();

        $games = [
            [
                'title' => 'Geometry Dash Full Version',
                'description' => 'Jump and fly your way through danger in this rhythm-based action platformer! Prepare for a near impossible challenge in the world of Geometry Dash. Push your skills to the limit as you jump, fly and flip your way through dangerous passages and spiky obstacles.',
                'instructions' => 'Use SPACE or UP arrow to jump. Hold to jump higher. In ship mode, hold to fly up and release to fly down. Yellow rings give you a higher jump. Blue rings give you a higher jump in ship mode. Collect the coins to unlock new characters!',
                'source_type' => 'iframe',
                'iframe_url' => 'https://scratch.mit.edu/projects/105500895/embed',
                'developer' => 'RobTop Games',
                'publisher' => 'RobTop Games',
                'release_date' => '2013-08-13',
                'version' => '2.11',
                'supported_devices' => ['desktop', 'mobile', 'tablet'],
                'is_featured' => true,
                'is_trending' => true,
                'play_count' => 15420,
                'daily_plays' => 234,
                'weekly_plays' => 1456,
                'monthly_plays' => 5678,
                'rating' => 4.8,
                'rating_count' => 892,
                'meta_title' => 'Geometry Dash Full Version - Play Free Online',
                'meta_description' => 'Play Geometry Dash Full Version online for free. Jump and fly through dangerous passages in this rhythm-based action platformer.',
                'meta_keywords' => 'geometry dash, platformer, rhythm game, action game, free online game',
            ],
            [
                'title' => 'Super Mario Flash',
                'description' => 'A flash remake of the classic Super Mario Bros game. Run, jump and collect coins while avoiding enemies in this nostalgic platformer adventure.',
                'instructions' => 'Use arrow keys to move and jump. Collect coins and power-ups while avoiding enemies. Reach the flag at the end of each level to progress.',
                'source_type' => 'iframe',
                'iframe_url' => 'https://www.crazygames.com/embed/super-mario-flash',
                'developer' => 'Pouetpu Games',
                'publisher' => 'Pouetpu Games',
                'release_date' => '2007-03-15',
                'supported_devices' => ['desktop'],
                'is_featured' => true,
                'play_count' => 8934,
                'daily_plays' => 156,
                'weekly_plays' => 987,
                'monthly_plays' => 3421,
                'rating' => 4.6,
                'rating_count' => 567,
                'meta_title' => 'Super Mario Flash - Classic Platform Game',
                'meta_description' => 'Play Super Mario Flash, a faithful remake of the classic Nintendo game. Run, jump and collect coins in this nostalgic adventure.',
                'meta_keywords' => 'super mario, flash game, platformer, classic game, nintendo',
            ],
            [
                'title' => 'Tetris',
                'description' => 'The classic puzzle game that started it all. Arrange falling blocks to create complete lines and clear them from the board. How long can you last?',
                'instructions' => 'Use arrow keys to move and rotate pieces. Down arrow to drop faster. Complete horizontal lines to clear them and score points.',
                'source_type' => 'iframe',
                'iframe_url' => 'https://tetris.com/play-tetris',
                'developer' => 'Alexey Pajitnov',
                'publisher' => 'The Tetris Company',
                'release_date' => '1984-06-06',
                'supported_devices' => ['desktop', 'mobile', 'tablet'],
                'is_featured' => true,
                'play_count' => 12567,
                'daily_plays' => 289,
                'weekly_plays' => 1834,
                'monthly_plays' => 6789,
                'rating' => 4.9,
                'rating_count' => 1234,
                'meta_title' => 'Tetris - Classic Puzzle Game Online',
                'meta_description' => 'Play the classic Tetris puzzle game online. Arrange falling blocks to create lines and achieve the highest score possible.',
                'meta_keywords' => 'tetris, puzzle game, classic game, blocks, arcade',
            ],
            [
                'title' => 'Pac-Man',
                'description' => 'Navigate through the maze, eat all the dots, and avoid the ghosts in this timeless arcade classic. Collect power pellets to turn the tables on your pursuers!',
                'instructions' => 'Use arrow keys to move Pac-Man through the maze. Eat all dots to complete the level. Avoid ghosts unless you eat a power pellet, then you can eat them for bonus points.',
                'source_type' => 'iframe',
                'iframe_url' => 'https://www.google.com/logos/2010/pacman10-i.html',
                'developer' => 'Namco',
                'publisher' => 'Namco',
                'release_date' => '1980-05-22',
                'supported_devices' => ['desktop', 'mobile', 'tablet'],
                'is_featured' => false,
                'is_trending' => true,
                'play_count' => 9876,
                'daily_plays' => 198,
                'weekly_plays' => 1234,
                'monthly_plays' => 4567,
                'rating' => 4.7,
                'rating_count' => 789,
                'meta_title' => 'Pac-Man - Classic Arcade Game',
                'meta_description' => 'Play the original Pac-Man arcade game online. Navigate mazes, eat dots, and avoid ghosts in this timeless classic.',
                'meta_keywords' => 'pac-man, arcade game, classic game, maze, namco',
            ],
            [
                'title' => 'Snake Game',
                'description' => 'Control a growing snake as it moves around the board eating food. Avoid hitting the walls or your own tail in this simple yet addictive game.',
                'instructions' => 'Use arrow keys to control the snake. Eat food to grow longer and score points. Avoid hitting walls or your own body.',
                'source_type' => 'iframe',
                'iframe_url' => 'https://playsnake.org/',
                'developer' => 'Classic Games',
                'publisher' => 'Retro Games Inc',
                'release_date' => '1976-01-01',
                'supported_devices' => ['desktop', 'mobile', 'tablet'],
                'is_featured' => false,
                'play_count' => 5432,
                'daily_plays' => 87,
                'weekly_plays' => 543,
                'monthly_plays' => 2109,
                'rating' => 4.3,
                'rating_count' => 321,
                'meta_title' => 'Snake Game - Classic Retro Game',
                'meta_description' => 'Play the classic Snake game online. Control a growing snake and eat food while avoiding obstacles.',
                'meta_keywords' => 'snake game, retro game, classic game, arcade',
            ],
        ];

        foreach ($games as $gameData) {
            $game = Game::create([
                'title' => $gameData['title'],
                'slug' => Str::slug($gameData['title']),
                'description' => $gameData['description'],
                'instructions' => $gameData['instructions'],
                'source_type' => $gameData['source_type'],
                'iframe_url' => $gameData['iframe_url'],
                'developer' => $gameData['developer'],
                'publisher' => $gameData['publisher'],
                'release_date' => $gameData['release_date'],
                'version' => $gameData['version'] ?? null,
                'supported_devices' => $gameData['supported_devices'],
                'is_active' => true,
                'is_featured' => $gameData['is_featured'],
                'is_trending' => $gameData['is_trending'] ?? false,
                'play_count' => $gameData['play_count'],
                'daily_plays' => $gameData['daily_plays'],
                'weekly_plays' => $gameData['weekly_plays'],
                'monthly_plays' => $gameData['monthly_plays'],
                'rating' => $gameData['rating'],
                'rating_count' => $gameData['rating_count'],
                'meta_title' => $gameData['meta_title'],
                'meta_description' => $gameData['meta_description'],
                'meta_keywords' => $gameData['meta_keywords'],
                'created_by' => $admin->id,
                'updated_by' => $admin->id,
            ]);

            // Assign random categories (1-3 per game)
            $randomCategories = $categories->random(rand(1, 3));
            $game->categories()->attach($randomCategories->pluck('id'));
            
            // Set primary category
            $game->update(['primary_category_id' => $randomCategories->first()->id]);

            // Assign random tags (3-8 per game)
            $randomTags = $tags->random(rand(3, 8));
            $game->tags()->attach($randomTags->pluck('id'));
            
            // Update tag usage counts
            foreach ($randomTags as $tag) {
                $tag->increment('usage_count');
            }
        }
    }
}
