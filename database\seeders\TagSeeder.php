<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Tag;
use Illuminate\Support\Str;

class TagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tags = [
            'HTML5', 'WebGL', 'Unity', 'Construct 3', 'Phaser',
            'Single Player', 'Multiplayer', 'Mobile Friendly', 'Fullscreen',
            'Retro', 'Pixel Art', '3D', '2D', 'Minimalist',
            'Challenging', 'Casual', 'Hardcore', 'Family Friendly',
            'Physics', 'Simulation', 'Educational', 'Music',
            'Endless', 'Level Based', 'Story Mode', 'Sandbox',
            'Competitive', 'Cooperative', 'Turn Based', 'Real Time',
            'Fantasy', 'Sci-Fi', 'Horror', 'Comedy',
            'Medieval', 'Modern', 'Futuristic', 'Historical',
            'Animals', 'Cars', 'Space', 'Underwater',
            'Ninja', 'Zombie', 'Robot', 'Magic',
            'Shooting', 'Fighting', 'Jumping', 'Running',
            'Building', 'Crafting', 'Collecting', 'Matching',
        ];

        foreach ($tags as $index => $tagName) {
            Tag::create([
                'name' => $tagName,
                'slug' => Str::slug($tagName),
                'description' => "Games tagged with {$tagName}",
                'color' => sprintf('#%06X', mt_rand(0, 0xFFFFFF)),
                'is_active' => true,
                'usage_count' => 0,
                'meta_title' => "{$tagName} Games - Free Online",
                'meta_description' => "Play the best {$tagName} games online for free. No downloads required!",
            ]);
        }
    }
}
