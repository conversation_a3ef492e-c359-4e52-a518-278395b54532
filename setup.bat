@echo off
echo ========================================
echo   Geometry Dash CMS - Setup Script
echo ========================================
echo.

echo Checking PHP installation...
php --version
if %errorlevel% neq 0 (
    echo ERROR: PHP is not installed or not in PATH
    echo Please install PHP 8.2+ from https://windows.php.net/download/
    pause
    exit /b 1
)

echo.
echo Checking Composer installation...
composer --version
if %errorlevel% neq 0 (
    echo ERROR: Composer is not installed
    echo Please install Composer from https://getcomposer.org/download/
    pause
    exit /b 1
)

echo.
echo Checking Node.js installation...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo.
echo Installing PHP dependencies...
composer install

echo.
echo Installing Node.js dependencies...
npm install

echo.
echo Copying environment file...
if not exist .env (
    copy .env.example .env
)

echo.
echo Generating application key...
php artisan key:generate

echo.
echo Creating storage link...
php artisan storage:link

echo.
echo Running database migrations...
php artisan migrate

echo.
echo Seeding database with sample data...
php artisan db:seed

echo.
echo Building frontend assets...
npm run build

echo.
echo ========================================
echo   Setup Complete!
echo ========================================
echo.
echo To start the development server, run:
echo   php artisan serve
echo.
echo Then visit: http://localhost:8000
echo Admin panel: http://localhost:8000/admin
echo.
echo Default admin credentials:
echo   Email: <EMAIL>
echo   Password: password
echo.
pause
