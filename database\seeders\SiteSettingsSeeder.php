<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SiteSetting;

class SiteSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'Geometry Dash Full Version',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Site Name',
                'description' => 'The name of your website',
                'sort_order' => 1,
                'is_public' => true,
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Play Free HTML5 Games Online',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Site Tagline',
                'description' => 'A short description of your site',
                'sort_order' => 2,
                'is_public' => true,
            ],
            [
                'key' => 'site_logo',
                'value' => null,
                'type' => 'file',
                'group' => 'general',
                'label' => 'Site Logo',
                'description' => 'Upload your site logo',
                'sort_order' => 3,
                'is_public' => true,
            ],
            [
                'key' => 'site_favicon',
                'value' => null,
                'type' => 'file',
                'group' => 'general',
                'label' => 'Site Favicon',
                'description' => 'Upload your site favicon (16x16 or 32x32 pixels)',
                'sort_order' => 4,
                'is_public' => true,
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Contact Email',
                'description' => 'Main contact email address',
                'sort_order' => 5,
                'is_public' => true,
            ],

            // SEO Settings
            [
                'key' => 'meta_description',
                'value' => 'Play Geometry Dash Full Version and thousands of other HTML5 games online for free. No downloads required!',
                'type' => 'textarea',
                'group' => 'seo',
                'label' => 'Meta Description',
                'description' => 'Default meta description for your site',
                'sort_order' => 1,
                'is_public' => true,
            ],
            [
                'key' => 'meta_keywords',
                'value' => 'geometry dash, html5 games, online games, free games, browser games',
                'type' => 'textarea',
                'group' => 'seo',
                'label' => 'Meta Keywords',
                'description' => 'Default meta keywords for your site',
                'sort_order' => 2,
                'is_public' => true,
            ],
            [
                'key' => 'og_image',
                'value' => null,
                'type' => 'file',
                'group' => 'seo',
                'label' => 'Open Graph Image',
                'description' => 'Default image for social media sharing (1200x630 pixels)',
                'sort_order' => 3,
                'is_public' => true,
            ],

            // Analytics Settings
            [
                'key' => 'google_analytics_id',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'label' => 'Google Analytics ID',
                'description' => 'Your Google Analytics tracking ID (e.g., G-XXXXXXXXXX)',
                'sort_order' => 1,
                'is_public' => true,
            ],
            [
                'key' => 'google_tag_manager_id',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'label' => 'Google Tag Manager ID',
                'description' => 'Your Google Tag Manager container ID (e.g., GTM-XXXXXXX)',
                'sort_order' => 2,
                'is_public' => true,
            ],

            // Advertising Settings
            [
                'key' => 'google_adsense_client',
                'value' => '',
                'type' => 'text',
                'group' => 'ads',
                'label' => 'Google AdSense Client ID',
                'description' => 'Your Google AdSense client ID (e.g., ca-pub-xxxxxxxxxxxxxxxx)',
                'sort_order' => 1,
                'is_public' => true,
            ],
            [
                'key' => 'ads_enabled',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'ads',
                'label' => 'Enable Advertisements',
                'description' => 'Enable or disable advertisements on your site',
                'sort_order' => 2,
                'is_public' => true,
            ],

            // Theme Settings
            [
                'key' => 'primary_color',
                'value' => '#ff6b35',
                'type' => 'text',
                'group' => 'theme',
                'label' => 'Primary Color',
                'description' => 'Main theme color (hex code)',
                'sort_order' => 1,
                'is_public' => true,
            ],
            [
                'key' => 'secondary_color',
                'value' => '#e55a2b',
                'type' => 'text',
                'group' => 'theme',
                'label' => 'Secondary Color',
                'description' => 'Secondary theme color (hex code)',
                'sort_order' => 2,
                'is_public' => true,
            ],
            [
                'key' => 'games_per_page',
                'value' => '12',
                'type' => 'text',
                'group' => 'theme',
                'label' => 'Games Per Page',
                'description' => 'Number of games to display per page',
                'sort_order' => 3,
                'is_public' => true,
            ],
            [
                'key' => 'featured_games_count',
                'value' => '6',
                'type' => 'text',
                'group' => 'theme',
                'label' => 'Featured Games Count',
                'description' => 'Number of featured games to display on homepage',
                'sort_order' => 4,
                'is_public' => true,
            ],
        ];

        foreach ($settings as $setting) {
            SiteSetting::create($setting);
        }
    }
}
