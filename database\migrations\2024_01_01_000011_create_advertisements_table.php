<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('advertisements', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('type', ['adsense', 'banner', 'custom'])->default('banner');
            $table->text('content'); // Ad code or banner HTML
            $table->string('position'); // header, before_game, after_game, sidebar, footer
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->json('display_rules')->nullable(); // Rules for when to show ad
            $table->integer('click_count')->default(0);
            $table->integer('impression_count')->default(0);
            $table->timestamps();
            
            $table->index(['position', 'is_active', 'sort_order']);
            $table->index(['start_date', 'end_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advertisements');
    }
};
