# 🚀 Complete Setup Guide for Geometry Dash CMS

## Step-by-Step Installation Instructions

### 📋 **STEP 1: Install Required Software**

#### **1.1 Install PHP 8.2+**

1. **Download PHP:**
   - Go to: https://windows.php.net/download/
   - Download **PHP 8.2+ Thread Safe (x64)** ZIP file
   - Extract to `C:\php`

2. **Configure PHP:**
   - Copy `C:\php\php.ini-development` to `C:\php\php.ini`
   - Edit `php.ini` and uncomment these lines (remove the `;`):
   ```ini
   extension=curl
   extension=fileinfo
   extension=gd
   extension=mbstring
   extension=openssl
   extension=pdo_mysql
   extension=pdo_sqlite
   extension=sqlite3
   extension=zip
   ```

3. **Add PHP to PATH:**
   - Press `Win + R`, type `sysdm.cpl`, press Enter
   - Click "Environment Variables"
   - Under "System Variables", find "Path" and click "Edit"
   - Click "New" and add: `C:\php`
   - Click "OK" to save

4. **Verify PHP Installation:**
   - Open Command Prompt (Win + R, type `cmd`)
   - Type: `php --version`
   - You should see PHP version information

#### **1.2 Install Composer**

1. **Download Composer:**
   - Go to: https://getcomposer.org/download/
   - Click "Composer-Setup.exe" to download the installer

2. **Install Composer:**
   - Run the downloaded installer
   - Follow the installation wizard
   - It should automatically detect your PHP installation

3. **Verify Composer:**
   - Open Command Prompt
   - Type: `composer --version`
   - You should see Composer version information

#### **1.3 Install Node.js**

1. **Download Node.js:**
   - Go to: https://nodejs.org/
   - Download the **LTS version** (recommended)

2. **Install Node.js:**
   - Run the downloaded installer
   - Follow the installation wizard (use default settings)

3. **Verify Node.js:**
   - Open Command Prompt
   - Type: `node --version` and `npm --version`
   - You should see version numbers for both

### 📁 **STEP 2: Set Up the Project**

#### **2.1 Navigate to Project Directory**

1. Open Command Prompt
2. Navigate to your project folder:
   ```cmd
   cd "d:\cms theme1"
   ```

#### **2.2 Install PHP Dependencies**

```cmd
composer install
```

**If you get errors:**
- Make sure PHP is in your PATH
- Check that all required PHP extensions are enabled
- Try: `composer install --ignore-platform-reqs` (temporary fix)

#### **2.3 Install Node.js Dependencies**

```cmd
npm install
```

#### **2.4 Set Up Environment File**

```cmd
copy .env.example .env
```

#### **2.5 Generate Application Key**

```cmd
php artisan key:generate
```

#### **2.6 Create Storage Link**

```cmd
php artisan storage:link
```

#### **2.7 Set Up Database**

```cmd
php artisan migrate
```

#### **2.8 Seed Database with Sample Data**

```cmd
php artisan db:seed
```

#### **2.9 Build Frontend Assets**

```cmd
npm run build
```

### 🎯 **STEP 3: Run the Application**

#### **3.1 Start the Development Server**

```cmd
php artisan serve
```

#### **3.2 Access the Application**

- **Frontend:** http://localhost:8000
- **Admin Panel:** http://localhost:8000/admin

#### **3.3 Login to Admin Panel**

- **Email:** <EMAIL>
- **Password:** password

### 🔧 **Troubleshooting Common Issues**

#### **Issue 1: "php is not recognized"**
- PHP is not in your PATH
- Add `C:\php` to your system PATH environment variable
- Restart Command Prompt

#### **Issue 2: "composer is not recognized"**
- Composer is not installed properly
- Reinstall Composer from https://getcomposer.org/download/

#### **Issue 3: "Extension not found" errors**
- Edit `C:\php\php.ini`
- Uncomment the required extensions (remove `;` at the beginning)
- Restart Command Prompt

#### **Issue 4: "Permission denied" errors**
- Run Command Prompt as Administrator
- Or change directory permissions

#### **Issue 5: Database errors**
- Make sure SQLite extension is enabled in PHP
- Try: `php artisan migrate:fresh --seed`

#### **Issue 6: Assets not loading**
- Run: `npm run build`
- Check if `public/build` folder exists

### 🎮 **STEP 4: Using the CMS**

#### **4.1 Adding Games**

1. Go to Admin Panel: http://localhost:8000/admin
2. Click "Games" → "Create"
3. Choose game source:
   - **Iframe URL:** Enter the game's embed URL
   - **ZIP Upload:** Upload a ZIP file containing the game

#### **4.2 Managing Categories**

1. Go to "Categories" in admin panel
2. Create categories with custom colors and icons
3. Assign games to categories

#### **4.3 Site Configuration**

1. Go to "Site Settings" in admin panel
2. Configure SEO, analytics, and theme options
3. Update site name, description, etc.

### 📱 **Development Commands**

```cmd
# Start development server
php artisan serve

# Watch for file changes (in separate terminal)
npm run dev

# Build for production
npm run build

# Clear caches
php artisan optimize:clear

# Reset database
php artisan migrate:fresh --seed
```

### 🆘 **Getting Help**

If you encounter issues:

1. **Check PHP Configuration:**
   ```cmd
   php -m
   ```
   This shows all enabled PHP modules

2. **Check Laravel Requirements:**
   ```cmd
   php artisan about
   ```

3. **View Logs:**
   - Check `storage/logs/laravel.log` for errors

4. **Common Solutions:**
   ```cmd
   # Clear all caches
   php artisan optimize:clear
   
   # Regenerate autoload files
   composer dump-autoload
   
   # Fix permissions (if on Linux/Mac)
   chmod -R 775 storage bootstrap/cache
   ```

### ✅ **Success Checklist**

- [ ] PHP 8.2+ installed and in PATH
- [ ] Composer installed and working
- [ ] Node.js and NPM installed
- [ ] Project dependencies installed (`composer install`)
- [ ] Frontend dependencies installed (`npm install`)
- [ ] Environment file created (`.env`)
- [ ] Application key generated
- [ ] Database migrated and seeded
- [ ] Assets built (`npm run build`)
- [ ] Development server running (`php artisan serve`)
- [ ] Can access frontend at http://localhost:8000
- [ ] Can access admin at http://localhost:8000/admin
- [ ] Can login with admin credentials

**🎉 Congratulations! Your Geometry Dash CMS is now running!**
