<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Advertisement extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'content',
        'position',
        'is_active',
        'sort_order',
        'start_date',
        'end_date',
        'display_rules',
        'click_count',
        'impression_count',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'start_date' => 'date',
        'end_date' => 'date',
        'display_rules' => 'array',
        'click_count' => 'integer',
        'impression_count' => 'integer',
    ];

    /**
     * Scope a query to only include active advertisements.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by position.
     */
    public function scopePosition($query, $position)
    {
        return $query->where('position', $position);
    }

    /**
     * Scope a query to only include currently valid advertisements.
     */
    public function scopeValid($query)
    {
        $now = Carbon::now();
        
        return $query->where(function ($q) use ($now) {
            $q->where(function ($subQ) use ($now) {
                $subQ->whereNull('start_date')
                     ->orWhere('start_date', '<=', $now);
            })->where(function ($subQ) use ($now) {
                $subQ->whereNull('end_date')
                     ->orWhere('end_date', '>=', $now);
            });
        });
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Check if the advertisement is currently valid.
     */
    public function isValid(): bool
    {
        $now = Carbon::now();
        
        if ($this->start_date && $this->start_date->gt($now)) {
            return false;
        }
        
        if ($this->end_date && $this->end_date->lt($now)) {
            return false;
        }
        
        return true;
    }

    /**
     * Check if the advertisement should be displayed based on rules.
     */
    public function shouldDisplay(array $context = []): bool
    {
        if (!$this->is_active || !$this->isValid()) {
            return false;
        }
        
        if (!$this->display_rules) {
            return true;
        }
        
        // Check display rules
        foreach ($this->display_rules as $rule => $value) {
            switch ($rule) {
                case 'device_types':
                    if (isset($context['device_type']) && !in_array($context['device_type'], $value)) {
                        return false;
                    }
                    break;
                    
                case 'countries':
                    if (isset($context['country_code']) && !in_array($context['country_code'], $value)) {
                        return false;
                    }
                    break;
                    
                case 'pages':
                    if (isset($context['page']) && !in_array($context['page'], $value)) {
                        return false;
                    }
                    break;
                    
                case 'categories':
                    if (isset($context['category_id']) && !in_array($context['category_id'], $value)) {
                        return false;
                    }
                    break;
                    
                case 'max_impressions_per_day':
                    // This would require additional tracking logic
                    break;
            }
        }
        
        return true;
    }

    /**
     * Increment the impression count.
     */
    public function recordImpression(): void
    {
        $this->increment('impression_count');
    }

    /**
     * Increment the click count.
     */
    public function recordClick(): void
    {
        $this->increment('click_count');
    }

    /**
     * Get the click-through rate.
     */
    public function getClickThroughRateAttribute(): float
    {
        if ($this->impression_count === 0) {
            return 0;
        }
        
        return round(($this->click_count / $this->impression_count) * 100, 2);
    }

    /**
     * Get advertisements for a specific position.
     */
    public static function getForPosition(string $position, array $context = []): \Illuminate\Database\Eloquent\Collection
    {
        $ads = static::active()
                    ->valid()
                    ->position($position)
                    ->ordered()
                    ->get();
        
        return $ads->filter(function ($ad) use ($context) {
            return $ad->shouldDisplay($context);
        });
    }
}
