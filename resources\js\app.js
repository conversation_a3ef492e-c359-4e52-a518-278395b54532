import './bootstrap';
import Alpine from 'alpinejs';

window.Alpine = Alpine;

Alpine.start();

// Game iframe management
window.GameManager = {
    currentGame: null,
    
    playGame(gameId, iframeUrl) {
        const gameFrame = document.getElementById('game-frame');
        const playButton = document.getElementById('play-button');
        const loadingScreen = document.getElementById('loading-screen');
        const fullscreenButton = document.getElementById('fullscreen-button');
        
        if (!gameFrame || !playButton || !loadingScreen) return;
        
        // Hide play button and show loading
        playButton.style.display = 'none';
        loadingScreen.style.display = 'flex';
        
        // Create iframe
        const iframe = document.createElement('iframe');
        iframe.src = iframeUrl;
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.border = 'none';
        iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-forms');
        iframe.setAttribute('allowfullscreen', 'true');
        
        // Handle iframe load
        iframe.onload = () => {
            loadingScreen.style.display = 'none';
            gameFrame.appendChild(iframe);
            if (fullscreenButton) {
                fullscreenButton.style.display = 'block';
            }
            
            // Track play
            this.trackPlay(gameId);
        };
        
        // Handle iframe error
        iframe.onerror = () => {
            loadingScreen.style.display = 'none';
            playButton.style.display = 'block';
            alert('Failed to load game. Please try again.');
        };
        
        this.currentGame = { id: gameId, iframe: iframe };
    },
    
    toggleFullscreen() {
        const gameFrame = document.getElementById('game-frame');
        if (!gameFrame) return;
        
        if (!document.fullscreenElement) {
            gameFrame.requestFullscreen().catch(err => {
                console.error('Error attempting to enable fullscreen:', err);
            });
        } else {
            document.exitFullscreen();
        }
    },
    
    trackPlay(gameId) {
        fetch(`/api/games/${gameId}/track-play`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        }).catch(err => {
            console.error('Failed to track play:', err);
        });
    }
};

// Search functionality
window.SearchManager = {
    debounceTimer: null,
    
    search(query) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
            if (query.length >= 2) {
                window.location.href = `/search?q=${encodeURIComponent(query)}`;
            }
        }, 500);
    },
    
    filterByCategory(categorySlug) {
        window.location.href = `/category/${categorySlug}`;
    },
    
    filterByTag(tagSlug) {
        window.location.href = `/tag/${tagSlug}`;
    }
};

// Mobile menu toggle
window.toggleMobileMenu = function() {
    const mobileMenu = document.getElementById('mobile-menu');
    if (mobileMenu) {
        mobileMenu.classList.toggle('hidden');
    }
};

// Lazy loading for game images
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
});
