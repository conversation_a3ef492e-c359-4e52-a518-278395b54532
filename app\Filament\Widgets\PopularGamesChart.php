<?php

namespace App\Filament\Widgets;

use App\Models\Game;
use Filament\Widgets\ChartWidget;

class PopularGamesChart extends ChartWidget
{
    protected static ?string $heading = 'Most Popular Games';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $games = Game::where('is_active', true)
            ->orderBy('play_count', 'desc')
            ->limit(10)
            ->get(['title', 'play_count']);

        return [
            'datasets' => [
                [
                    'label' => 'Play Count',
                    'data' => $games->pluck('play_count')->toArray(),
                    'backgroundColor' => [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40',
                        '#FF6384',
                        '#C9CBCF',
                        '#4BC0C0',
                        '#FF6384',
                    ],
                ],
            ],
            'labels' => $games->pluck('title')->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }
}
