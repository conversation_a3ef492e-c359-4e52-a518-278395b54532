<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Action Games',
                'description' => 'Fast-paced games with exciting gameplay and challenges',
                'color' => '#EF4444',
                'icon' => 'heroicon-o-bolt',
                'sort_order' => 1,
            ],
            [
                'name' => 'Puzzle Games',
                'description' => 'Brain-teasing games that challenge your problem-solving skills',
                'color' => '#8B5CF6',
                'icon' => 'heroicon-o-puzzle-piece',
                'sort_order' => 2,
            ],
            [
                'name' => 'Adventure Games',
                'description' => 'Explore new worlds and embark on epic journeys',
                'color' => '#10B981',
                'icon' => 'heroicon-o-map',
                'sort_order' => 3,
            ],
            [
                'name' => 'Racing Games',
                'description' => 'High-speed racing and driving games',
                'color' => '#F59E0B',
                'icon' => 'heroicon-o-truck',
                'sort_order' => 4,
            ],
            [
                'name' => 'Sports Games',
                'description' => 'Virtual sports and athletic competitions',
                'color' => '#06B6D4',
                'icon' => 'heroicon-o-trophy',
                'sort_order' => 5,
            ],
            [
                'name' => 'Strategy Games',
                'description' => 'Plan, build, and conquer in strategic gameplay',
                'color' => '#84CC16',
                'icon' => 'heroicon-o-chess-piece',
                'sort_order' => 6,
            ],
            [
                'name' => 'Arcade Games',
                'description' => 'Classic arcade-style games with simple, fun gameplay',
                'color' => '#EC4899',
                'icon' => 'heroicon-o-device-phone-mobile',
                'sort_order' => 7,
            ],
            [
                'name' => 'Platform Games',
                'description' => 'Jump, run, and navigate through challenging platforms',
                'color' => '#F97316',
                'icon' => 'heroicon-o-squares-2x2',
                'sort_order' => 8,
            ],
        ];

        foreach ($categories as $category) {
            Category::create([
                'name' => $category['name'],
                'slug' => Str::slug($category['name']),
                'description' => $category['description'],
                'color' => $category['color'],
                'icon' => $category['icon'],
                'sort_order' => $category['sort_order'],
                'is_active' => true,
                'meta_title' => $category['name'] . ' - Free Online Games',
                'meta_description' => $category['description'] . ' Play for free online.',
            ]);
        }
    }
}
