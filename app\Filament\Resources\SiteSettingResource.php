<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SiteSettingResource\Pages;
use App\Models\SiteSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class SiteSettingResource extends Resource
{
    protected static ?string $model = SiteSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationGroup = 'Settings';

    protected static ?string $navigationLabel = 'Site Settings';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Setting Information')
                    ->schema([
                        Forms\Components\TextInput::make('key')
                            ->required()
                            ->maxLength(255)
                            ->unique(SiteSetting::class, 'key', ignoreRecord: true),
                        
                        Forms\Components\TextInput::make('label')
                            ->required()
                            ->maxLength(255),
                        
                        Forms\Components\Select::make('type')
                            ->required()
                            ->options([
                                'text' => 'Text',
                                'textarea' => 'Textarea',
                                'boolean' => 'Boolean',
                                'integer' => 'Integer',
                                'float' => 'Float',
                                'json' => 'JSON',
                                'file' => 'File',
                            ])
                            ->live(),
                        
                        Forms\Components\Select::make('group')
                            ->required()
                            ->options([
                                'general' => 'General',
                                'seo' => 'SEO',
                                'analytics' => 'Analytics',
                                'ads' => 'Advertising',
                                'theme' => 'Theme',
                                'social' => 'Social Media',
                                'email' => 'Email',
                                'security' => 'Security',
                            ]),
                        
                        Forms\Components\Textarea::make('description')
                            ->rows(2),
                        
                        Forms\Components\TextInput::make('sort_order')
                            ->numeric()
                            ->default(0),
                        
                        Forms\Components\Toggle::make('is_public')
                            ->default(true)
                            ->helperText('Whether this setting should be available on the frontend'),
                    ])->columns(2),

                Forms\Components\Section::make('Setting Value')
                    ->schema([
                        Forms\Components\TextInput::make('value')
                            ->label('Value')
                            ->visible(fn (Forms\Get $get): bool => in_array($get('type'), ['text', 'integer', 'float']))
                            ->required(fn (Forms\Get $get): bool => in_array($get('type'), ['text', 'integer', 'float'])),
                        
                        Forms\Components\Textarea::make('value')
                            ->label('Value')
                            ->rows(4)
                            ->visible(fn (Forms\Get $get): bool => in_array($get('type'), ['textarea', 'json']))
                            ->required(fn (Forms\Get $get): bool => in_array($get('type'), ['textarea', 'json'])),
                        
                        Forms\Components\Toggle::make('value')
                            ->label('Value')
                            ->visible(fn (Forms\Get $get): bool => $get('type') === 'boolean'),
                        
                        Forms\Components\FileUpload::make('value')
                            ->label('File')
                            ->directory('settings')
                            ->visible(fn (Forms\Get $get): bool => $get('type') === 'file'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('group')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'general' => 'gray',
                        'seo' => 'success',
                        'analytics' => 'info',
                        'ads' => 'warning',
                        'theme' => 'primary',
                        'social' => 'purple',
                        'email' => 'orange',
                        'security' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('label')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('key')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                Tables\Columns\TextColumn::make('type')
                    ->badge(),
                
                Tables\Columns\TextColumn::make('value')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),
                
                Tables\Columns\IconColumn::make('is_public')
                    ->boolean(),
                
                Tables\Columns\TextColumn::make('sort_order')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('group')
                    ->options([
                        'general' => 'General',
                        'seo' => 'SEO',
                        'analytics' => 'Analytics',
                        'ads' => 'Advertising',
                        'theme' => 'Theme',
                        'social' => 'Social Media',
                        'email' => 'Email',
                        'security' => 'Security',
                    ]),
                
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'text' => 'Text',
                        'textarea' => 'Textarea',
                        'boolean' => 'Boolean',
                        'integer' => 'Integer',
                        'float' => 'Float',
                        'json' => 'JSON',
                        'file' => 'File',
                    ]),
                
                Tables\Filters\Filter::make('is_public')
                    ->query(fn ($query) => $query->where('is_public', true)),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('group')
            ->groups([
                Tables\Grouping\Group::make('group')
                    ->label('Group')
                    ->collapsible(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSiteSettings::route('/'),
            'create' => Pages\CreateSiteSetting::route('/create'),
            'edit' => Pages\EditSiteSetting::route('/{record}/edit'),
        ];
    }
}
