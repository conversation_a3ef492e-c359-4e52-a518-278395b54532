<?php

namespace App\Filament\Resources;

use App\Filament\Resources\GameResource\Pages;
use App\Models\Game;
use App\Models\Category;
use App\Models\Tag;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class GameResource extends Resource
{
    protected static ?string $model = Game::class;

    protected static ?string $navigationIcon = 'heroicon-o-puzzle-piece';

    protected static ?string $navigationGroup = 'Content Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn (string $context, $state, Forms\Set $set) => $context === 'create' ? $set('slug', Str::slug($state)) : null),
                        
                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(Game::class, 'slug', ignoreRecord: true),
                        
                        Forms\Components\Textarea::make('description')
                            ->required()
                            ->rows(3),
                        
                        Forms\Components\Textarea::make('instructions')
                            ->rows(3),
                        
                        Forms\Components\TextInput::make('developer')
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('publisher')
                            ->maxLength(255),
                        
                        Forms\Components\DatePicker::make('release_date'),
                        
                        Forms\Components\TextInput::make('version')
                            ->maxLength(50),
                    ])->columns(2),

                Forms\Components\Section::make('Game Source')
                    ->schema([
                        Forms\Components\Select::make('source_type')
                            ->required()
                            ->options([
                                'iframe' => 'Iframe URL',
                                'upload' => 'ZIP File Upload',
                            ])
                            ->live(),
                        
                        Forms\Components\TextInput::make('iframe_url')
                            ->url()
                            ->visible(fn (Forms\Get $get): bool => $get('source_type') === 'iframe')
                            ->required(fn (Forms\Get $get): bool => $get('source_type') === 'iframe'),
                        
                        Forms\Components\FileUpload::make('game_file_path')
                            ->label('Game ZIP File')
                            ->acceptedFileTypes(['application/zip'])
                            ->directory('games')
                            ->visible(fn (Forms\Get $get): bool => $get('source_type') === 'upload')
                            ->required(fn (Forms\Get $get): bool => $get('source_type') === 'upload'),
                        
                        Forms\Components\TextInput::make('game_index_file')
                            ->label('Index File (e.g., index.html)')
                            ->default('index.html')
                            ->visible(fn (Forms\Get $get): bool => $get('source_type') === 'upload'),
                    ]),

                Forms\Components\Section::make('Media')
                    ->schema([
                        Forms\Components\FileUpload::make('thumbnail')
                            ->image()
                            ->directory('games/thumbnails')
                            ->imageResizeMode('cover')
                            ->imageCropAspectRatio('16:9')
                            ->imageResizeTargetWidth('400')
                            ->imageResizeTargetHeight('225'),
                        
                        Forms\Components\FileUpload::make('screenshot')
                            ->image()
                            ->directory('games/screenshots')
                            ->imageResizeMode('cover')
                            ->imageCropAspectRatio('16:9')
                            ->imageResizeTargetWidth('800')
                            ->imageResizeTargetHeight('450'),
                    ])->columns(2),

                Forms\Components\Section::make('Categorization')
                    ->schema([
                        Forms\Components\Select::make('primary_category_id')
                            ->label('Primary Category')
                            ->relationship('primaryCategory', 'name')
                            ->searchable()
                            ->preload(),
                        
                        Forms\Components\Select::make('categories')
                            ->relationship('categories', 'name')
                            ->multiple()
                            ->searchable()
                            ->preload(),
                        
                        Forms\Components\Select::make('tags')
                            ->relationship('tags', 'name')
                            ->multiple()
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->required()
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(fn (string $context, $state, Forms\Set $set) => $context === 'create' ? $set('slug', Str::slug($state)) : null),
                                Forms\Components\TextInput::make('slug')
                                    ->required(),
                                Forms\Components\Textarea::make('description'),
                                Forms\Components\ColorPicker::make('color'),
                            ]),
                    ])->columns(1),

                Forms\Components\Section::make('Settings')
                    ->schema([
                        Forms\Components\CheckboxList::make('supported_devices')
                            ->options([
                                'desktop' => 'Desktop',
                                'mobile' => 'Mobile',
                                'tablet' => 'Tablet',
                            ])
                            ->default(['desktop', 'mobile', 'tablet']),
                        
                        Forms\Components\Toggle::make('is_active')
                            ->default(true),
                        
                        Forms\Components\Toggle::make('is_featured'),
                        
                        Forms\Components\Toggle::make('is_trending'),
                        
                        Forms\Components\TextInput::make('sort_order')
                            ->numeric()
                            ->default(0),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('thumbnail')
                    ->size(60),
                
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('primaryCategory.name')
                    ->label('Category')
                    ->badge()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('source_type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'iframe' => 'info',
                        'upload' => 'success',
                    }),
                
                Tables\Columns\TextColumn::make('play_count')
                    ->numeric()
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                
                Tables\Columns\IconColumn::make('is_featured')
                    ->boolean(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('primary_category_id')
                    ->relationship('primaryCategory', 'name')
                    ->label('Category'),
                
                Tables\Filters\Filter::make('is_active')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true)),
                
                Tables\Filters\Filter::make('is_featured')
                    ->query(fn (Builder $query): Builder => $query->where('is_featured', true)),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGames::route('/'),
            'create' => Pages\CreateGame::route('/create'),
            'edit' => Pages\EditGame::route('/{record}/edit'),
        ];
    }
}
