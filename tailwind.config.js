import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './vendor/filament/**/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
            },
            colors: {
                'game-orange': {
                    500: '#ff6b35',
                    600: '#e55a2b',
                    700: '#cc4a21',
                },
            },
            animation: {
                'spin-slow': 'spin 2s linear infinite',
            }
        },
    },

    plugins: [forms],
};
