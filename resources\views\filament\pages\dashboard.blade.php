<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Welcome Section -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg">
            <div class="p-6 text-gray-900 dark:text-gray-100">
                <h2 class="text-2xl font-bold mb-2">Welcome to Geometry Dash CMS</h2>
                <p class="text-gray-600 dark:text-gray-400">
                    Manage your HTML5 games, categories, and site content from this admin panel.
                </p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="{{ route('filament.admin.resources.games.create') }}" 
               class="bg-orange-500 hover:bg-orange-600 text-white p-4 rounded-lg transition-colors">
                <div class="flex items-center">
                    <x-heroicon-o-plus class="w-6 h-6 mr-2" />
                    <span class="font-semibold">Add New Game</span>
                </div>
            </a>
            
            <a href="{{ route('filament.admin.resources.categories.index') }}" 
               class="bg-blue-500 hover:bg-blue-600 text-white p-4 rounded-lg transition-colors">
                <div class="flex items-center">
                    <x-heroicon-o-folder class="w-6 h-6 mr-2" />
                    <span class="font-semibold">Manage Categories</span>
                </div>
            </a>
            
            <a href="{{ route('filament.admin.resources.games.index') }}" 
               class="bg-green-500 hover:bg-green-600 text-white p-4 rounded-lg transition-colors">
                <div class="flex items-center">
                    <x-heroicon-o-puzzle-piece class="w-6 h-6 mr-2" />
                    <span class="font-semibold">View All Games</span>
                </div>
            </a>
            
            <a href="{{ url('/') }}" target="_blank"
               class="bg-purple-500 hover:bg-purple-600 text-white p-4 rounded-lg transition-colors">
                <div class="flex items-center">
                    <x-heroicon-o-eye class="w-6 h-6 mr-2" />
                    <span class="font-semibold">View Site</span>
                </div>
            </a>
        </div>

        <!-- Widgets -->
        <x-filament-widgets::widgets
            :widgets="$this->getWidgets()"
            :columns="$this->getColumns()"
        />
    </div>
</x-filament-panels::page>
