<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('games', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('instructions')->nullable(); // How to play instructions
            $table->string('thumbnail')->nullable(); // Game thumbnail image
            $table->string('screenshot')->nullable(); // Game screenshot
            
            // Game source - either iframe URL or uploaded ZIP
            $table->enum('source_type', ['iframe', 'upload'])->default('iframe');
            $table->text('iframe_url')->nullable(); // For iframe games
            $table->string('game_file_path')->nullable(); // For uploaded ZIP games
            $table->string('game_index_file')->default('index.html'); // Entry point for uploaded games
            
            // Game metadata
            $table->string('developer')->nullable();
            $table->string('publisher')->nullable();
            $table->date('release_date')->nullable();
            $table->string('version')->nullable();
            $table->json('supported_devices')->nullable(); // ['mobile', 'tablet', 'desktop']
            $table->string('game_size')->nullable(); // File size for uploaded games
            
            // Status and visibility
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_trending')->default(false);
            $table->integer('sort_order')->default(0);
            
            // Statistics
            $table->bigInteger('play_count')->default(0);
            $table->bigInteger('daily_plays')->default(0);
            $table->bigInteger('weekly_plays')->default(0);
            $table->bigInteger('monthly_plays')->default(0);
            $table->decimal('rating', 3, 2)->default(0.00); // Average rating 0.00-5.00
            $table->integer('rating_count')->default(0);
            
            // SEO
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->json('schema_data')->nullable(); // JSON-LD schema data
            
            // Relationships
            $table->foreignId('primary_category_id')->nullable()->constrained('categories')->onDelete('set null');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['is_active', 'is_featured', 'sort_order']);
            $table->index(['is_active', 'play_count']);
            $table->index(['is_active', 'created_at']);
            $table->index('slug');
            $table->index('source_type');
            $table->index('primary_category_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('games');
    }
};
