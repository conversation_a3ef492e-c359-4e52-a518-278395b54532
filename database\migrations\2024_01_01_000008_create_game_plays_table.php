<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('game_plays', function (Blueprint $table) {
            $table->id();
            $table->foreignId('game_id')->constrained()->onDelete('cascade');
            $table->string('ip_address', 45)->nullable();
            $table->string('user_agent')->nullable();
            $table->string('referrer')->nullable();
            $table->string('country_code', 2)->nullable();
            $table->string('device_type')->nullable(); // mobile, tablet, desktop
            $table->timestamp('played_at');
            $table->integer('session_duration')->nullable(); // in seconds
            
            $table->index(['game_id', 'played_at']);
            $table->index(['played_at']);
            $table->index(['game_id', 'ip_address', 'played_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('game_plays');
    }
};
