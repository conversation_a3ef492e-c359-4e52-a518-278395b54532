Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Geometry Dash CMS - Setup Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check command success
function Test-Command {
    param($CommandName)
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ ERROR: $CommandName failed" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    } else {
        Write-Host "✓ $CommandName completed successfully" -ForegroundColor Green
    }
}

# Check PHP
Write-Host "Checking PHP installation..." -ForegroundColor Yellow
try {
    php --version | Out-Null
    Test-Command "PHP check"
} catch {
    Write-Host "✗ ERROR: PHP is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install PHP 8.2+ from https://windows.php.net/download/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Check Composer
Write-Host "Checking Composer installation..." -ForegroundColor Yellow
try {
    composer --version | Out-Null
    Test-Command "Composer check"
} catch {
    Write-Host "✗ ERROR: Composer is not installed" -ForegroundColor Red
    Write-Host "Please install Composer from https://getcomposer.org/download/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Check Node.js
Write-Host "Checking Node.js installation..." -ForegroundColor Yellow
try {
    node --version | Out-Null
    Test-Command "Node.js check"
} catch {
    Write-Host "✗ ERROR: Node.js is not installed" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Install PHP dependencies
Write-Host "Installing PHP dependencies..." -ForegroundColor Yellow
composer install
Test-Command "Composer install"

Write-Host ""

# Install Node.js dependencies
Write-Host "Installing Node.js dependencies..." -ForegroundColor Yellow
npm install
Test-Command "NPM install"

Write-Host ""

# Copy environment file
Write-Host "Setting up environment file..." -ForegroundColor Yellow
if (-not (Test-Path ".env")) {
    Copy-Item ".env.example" ".env"
    Write-Host "✓ Environment file created" -ForegroundColor Green
} else {
    Write-Host "✓ Environment file already exists" -ForegroundColor Green
}

Write-Host ""

# Generate application key
Write-Host "Generating application key..." -ForegroundColor Yellow
php artisan key:generate
Test-Command "Key generation"

Write-Host ""

# Create storage link
Write-Host "Creating storage link..." -ForegroundColor Yellow
php artisan storage:link
Test-Command "Storage link creation"

Write-Host ""

# Run database migrations
Write-Host "Running database migrations..." -ForegroundColor Yellow
php artisan migrate
Test-Command "Database migration"

Write-Host ""

# Seed database
Write-Host "Seeding database with sample data..." -ForegroundColor Yellow
php artisan db:seed
Test-Command "Database seeding"

Write-Host ""

# Build frontend assets
Write-Host "Building frontend assets..." -ForegroundColor Yellow
npm run build
Test-Command "Asset building"

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "   ✓ Setup Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "To start the development server, run:" -ForegroundColor Cyan
Write-Host "  php artisan serve --port=8001" -ForegroundColor White
Write-Host ""
Write-Host "Then visit:" -ForegroundColor Cyan
Write-Host "  Frontend: http://localhost:8001" -ForegroundColor White
Write-Host "  Admin Panel: http://localhost:8001/admin" -ForegroundColor White
Write-Host ""
Write-Host "Note: Using port 8001 to avoid conflicts with other projects" -ForegroundColor Yellow
Write-Host ""
Write-Host "Default admin credentials:" -ForegroundColor Cyan
Write-Host "  Email: <EMAIL>" -ForegroundColor White
Write-Host "  Password: password" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to continue"
