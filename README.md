# HTML5 Games CMS - Geometry Dash Full Version

A comprehensive Content Management System for managing HTML5 games with Laravel 11, Filament admin panel, and optimized frontend.

## Features

### 🎮 User Interface (Frontend)
- **Homepage**: Featured game display with iframe embedding
- **Game Management**: Support for iframe URLs and ZIP file uploads
- **Responsive Design**: Mobile, tablet, and desktop optimization
- **Search & Filter**: By name, tags, and categories
- **SEO Optimized**: Meta tags, JSON-LD schema, sitemap generation

### 🛠 Admin Panel (Backend)
- **Filament Admin Panel**: Modern, intuitive interface
- **Game CRUD**: Complete game management with metadata
- **Category/Tag Management**: Flexible categorization system
- **System Configuration**: Site settings, SEO, analytics integration
- **User Management**: Admin roles and permissions

### 🔑 Technical Features
- **Security**: CSP headers, iframe sandboxing, file validation
- **Performance**: Caching, lazy loading, asset optimization
- **Advertising**: Configurable ad placements and Google AdSense
- **Analytics**: Google Analytics integration and play tracking

## Technology Stack

- **Backend**: Laravel 11
- **Admin Panel**: Filament PHP
- **Frontend**: Blade Templates + Tailwind CSS + Alpine.js
- **Database**: SQLite (configurable to MySQL/PostgreSQL)
- **Build Tools**: Vite
- **Package Management**: Composer + NPM

## Installation

### Prerequisites
- PHP 8.2 or higher
- Composer
- Node.js & NPM
- SQLite (or MySQL/PostgreSQL)

### Setup Steps

1. **Install PHP Dependencies**
   ```bash
   composer install
   ```

2. **Install Node Dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database Setup**
   ```bash
   touch database/database.sqlite
   php artisan migrate
   php artisan db:seed
   ```

5. **Storage Setup**
   ```bash
   php artisan storage:link
   ```

6. **Build Assets**
   ```bash
   npm run build
   ```

7. **Start Development Server**
   ```bash
   php artisan serve
   ```

## Configuration

### Environment Variables

Key environment variables to configure:

```env
APP_NAME="Geometry Dash Full Version"
APP_URL=http://localhost

# SEO
SITE_DESCRIPTION="Play Geometry Dash Full Version and thousands of other HTML5 games online for free."
SITE_KEYWORDS="geometry dash, html5 games, online games, free games"

# Analytics
GOOGLE_ANALYTICS_ID=your_ga_id
GOOGLE_ADSENSE_CLIENT=your_adsense_client

# Game Storage
GAMES_STORAGE_PATH=public/games
MAX_GAME_FILE_SIZE=50000
ALLOWED_GAME_EXTENSIONS=zip,html
```

### Admin Panel Access

After installation, create an admin user:

```bash
php artisan make:filament-user
```

Access the admin panel at: `/admin`

## Project Structure

```
├── app/
│   ├── Filament/           # Admin panel resources
│   ├── Http/Controllers/   # Web controllers
│   ├── Models/            # Eloquent models
│   └── Services/          # Business logic services
├── database/
│   ├── migrations/        # Database migrations
│   └── seeders/          # Database seeders
├── resources/
│   ├── views/            # Blade templates
│   ├── css/              # Stylesheets
│   └── js/               # JavaScript files
├── public/
│   ├── games/            # Uploaded game files
│   └── images/           # Static images
└── storage/
    └── app/public/       # Public file storage
```

## Development Roadmap

### Phase 1 (Current)
- [x] Project setup and Laravel installation
- [ ] Database design and migrations
- [ ] Filament admin panel configuration
- [ ] Game management system
- [ ] Frontend layout and navigation

### Phase 2
- [ ] Game display and iframe integration
- [ ] Homepage content sections
- [ ] Search and filtering system
- [ ] SEO optimization

### Phase 3
- [ ] Security implementation
- [ ] Performance optimization
- [ ] Advertising integration
- [ ] System configuration panel

### Phase 4
- [ ] Testing and quality assurance
- [ ] Documentation completion
- [ ] Deployment preparation

## Security Features

- **CSRF Protection**: Laravel's built-in CSRF protection
- **XSS Prevention**: Input sanitization and output escaping
- **SQL Injection Protection**: Eloquent ORM with prepared statements
- **File Upload Security**: Validation and sandboxing
- **Iframe Security**: CSP headers and sandbox attributes

## Performance Optimizations

- **Caching**: Route, view, and database query caching
- **Lazy Loading**: Images and iframe content
- **Asset Optimization**: CSS/JS minification and compression
- **Database Indexing**: Optimized queries and indexes

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is open-sourced software licensed under the [MIT license](LICENSE).

## Support

For support and questions, please open an issue in the GitHub repository.
