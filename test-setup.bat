@echo off
echo Testing system requirements...
echo.

echo Testing PHP...
php --version
if %errorlevel% neq 0 (
    echo ERROR: PHP not found. Please install PHP 8.2+ from https://windows.php.net/download/
    echo.
    goto :end
)

echo.
echo Testing Composer...
composer --version
if %errorlevel% neq 0 (
    echo ERROR: Composer not found. Please install from https://getcomposer.org/download/
    echo.
    goto :end
)

echo.
echo Testing Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found. Please install from https://nodejs.org/
    echo.
    goto :end
)

echo.
echo Testing NPM...
npm --version
if %errorlevel% neq 0 (
    echo ERROR: NPM not found. Please install Node.js from https://nodejs.org/
    echo.
    goto :end
)

echo.
echo ========================================
echo All requirements are installed!
echo You can now run: setup.bat
echo ========================================

:end
pause
