Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Testing System Requirements" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Test PHP
Write-Host "Testing PHP..." -ForegroundColor Yellow
try {
    $phpVersion = php --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ PHP is installed:" -ForegroundColor Green
        Write-Host $phpVersion.Split("`n")[0] -ForegroundColor Gray
    } else {
        throw "PHP not found"
    }
} catch {
    Write-Host "✗ ERROR: PHP not found" -ForegroundColor Red
    Write-Host "Please install PHP 8.2+ from https://windows.php.net/download/" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Test Composer
Write-Host "Testing Composer..." -ForegroundColor Yellow
try {
    $composerVersion = composer --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Composer is installed:" -ForegroundColor Green
        Write-Host $composerVersion -ForegroundColor Gray
    } else {
        throw "Composer not found"
    }
} catch {
    Write-Host "✗ ERROR: Composer not found" -ForegroundColor Red
    Write-Host "Please install Composer from https://getcomposer.org/download/" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Test Node.js
Write-Host "Testing Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Node.js is installed:" -ForegroundColor Green
        Write-Host $nodeVersion -ForegroundColor Gray
    } else {
        throw "Node.js not found"
    }
} catch {
    Write-Host "✗ ERROR: Node.js not found" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Test NPM
Write-Host "Testing NPM..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ NPM is installed:" -ForegroundColor Green
        Write-Host "v$npmVersion" -ForegroundColor Gray
    } else {
        throw "NPM not found"
    }
} catch {
    Write-Host "✗ ERROR: NPM not found" -ForegroundColor Red
    Write-Host "NPM should come with Node.js. Please reinstall Node.js from https://nodejs.org/" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "   ✓ All requirements are installed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "You can now run the setup:" -ForegroundColor Cyan
Write-Host "  .\setup.ps1" -ForegroundColor White
Write-Host ""
Write-Host "Or use the batch file:" -ForegroundColor Cyan
Write-Host "  .\setup.bat" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to continue"
