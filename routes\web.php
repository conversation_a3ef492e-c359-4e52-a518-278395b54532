<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\GameController;
use App\Http\Controllers\SearchController;

Route::get('/', [HomeController::class, 'index'])->name('home');

Route::get('/game/{slug}', [GameController::class, 'show'])->name('game.show');

Route::get('/search', [SearchController::class, 'index'])->name('search');

Route::get('/category/{slug}', [GameController::class, 'category'])->name('category.show');

Route::get('/tag/{slug}', [GameController::class, 'tag'])->name('tag.show');

Route::get('/sitemap.xml', [HomeController::class, 'sitemap'])->name('sitemap');

// Game API routes for iframe loading
Route::prefix('api/games')->group(function () {
    Route::get('/{id}/play', [GameController::class, 'play'])->name('api.game.play');
    Route::post('/{id}/track-play', [GameController::class, 'trackPlay'])->name('api.game.track-play');
});

// Health check
Route::get('/up', function () {
    return response()->json(['status' => 'ok']);
});
