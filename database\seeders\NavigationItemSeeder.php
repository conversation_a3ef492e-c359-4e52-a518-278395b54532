<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NavigationItem;
use App\Models\Category;

class NavigationItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Main navigation items
        $mainNavItems = [
            [
                'title' => 'Home',
                'url' => '/',
                'type' => 'custom',
                'location' => 'main',
                'sort_order' => 1,
            ],
            [
                'title' => 'New Games',
                'url' => '/games/new',
                'type' => 'custom',
                'location' => 'main',
                'sort_order' => 2,
            ],
            [
                'title' => 'Popular Games',
                'url' => '/games/popular',
                'type' => 'custom',
                'location' => 'main',
                'sort_order' => 3,
            ],
            [
                'title' => 'Categories',
                'url' => '#',
                'type' => 'custom',
                'location' => 'main',
                'sort_order' => 4,
            ],
        ];

        foreach ($mainNavItems as $item) {
            $navItem = NavigationItem::create($item);
            
            // Add category submenu items for Categories
            if ($item['title'] === 'Categories') {
                $categories = Category::where('is_active', true)
                    ->orderBy('sort_order')
                    ->take(8)
                    ->get();
                
                foreach ($categories as $index => $category) {
                    NavigationItem::create([
                        'title' => $category->name,
                        'url' => "/category/{$category->slug}",
                        'type' => 'category',
                        'reference_id' => $category->id,
                        'location' => 'main',
                        'parent_id' => $navItem->id,
                        'sort_order' => $index + 1,
                    ]);
                }
            }
        }

        // Sidebar navigation items
        $sidebarNavItems = [
            [
                'title' => 'Action Games',
                'url' => '/category/action-games',
                'type' => 'category',
                'reference_id' => Category::where('slug', 'action-games')->first()?->id,
                'location' => 'sidebar',
                'sort_order' => 1,
            ],
            [
                'title' => 'Puzzle Games',
                'url' => '/category/puzzle-games',
                'type' => 'category',
                'reference_id' => Category::where('slug', 'puzzle-games')->first()?->id,
                'location' => 'sidebar',
                'sort_order' => 2,
            ],
            [
                'title' => 'Adventure Games',
                'url' => '/category/adventure-games',
                'type' => 'category',
                'reference_id' => Category::where('slug', 'adventure-games')->first()?->id,
                'location' => 'sidebar',
                'sort_order' => 3,
            ],
            [
                'title' => 'Racing Games',
                'url' => '/category/racing-games',
                'type' => 'category',
                'reference_id' => Category::where('slug', 'racing-games')->first()?->id,
                'location' => 'sidebar',
                'sort_order' => 4,
            ],
            [
                'title' => 'Sports Games',
                'url' => '/category/sports-games',
                'type' => 'category',
                'reference_id' => Category::where('slug', 'sports-games')->first()?->id,
                'location' => 'sidebar',
                'sort_order' => 5,
            ],
            [
                'title' => 'Strategy Games',
                'url' => '/category/strategy-games',
                'type' => 'category',
                'reference_id' => Category::where('slug', 'strategy-games')->first()?->id,
                'location' => 'sidebar',
                'sort_order' => 6,
            ],
        ];

        foreach ($sidebarNavItems as $item) {
            NavigationItem::create($item);
        }

        // Footer navigation items
        $footerNavItems = [
            [
                'title' => 'About Us',
                'url' => '/about',
                'type' => 'custom',
                'location' => 'footer',
                'sort_order' => 1,
            ],
            [
                'title' => 'Contact',
                'url' => '/contact',
                'type' => 'custom',
                'location' => 'footer',
                'sort_order' => 2,
            ],
            [
                'title' => 'Privacy Policy',
                'url' => '/privacy',
                'type' => 'custom',
                'location' => 'footer',
                'sort_order' => 3,
            ],
            [
                'title' => 'Terms of Service',
                'url' => '/terms',
                'type' => 'custom',
                'location' => 'footer',
                'sort_order' => 4,
            ],
        ];

        foreach ($footerNavItems as $item) {
            NavigationItem::create($item);
        }
    }
}
