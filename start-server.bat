@echo off
echo ========================================
echo   Starting Geometry Dash CMS Server
echo ========================================
echo.

if not exist .env (
    echo WARNING: Environment file not found!
    echo Please run setup first: setup.bat
    echo.
    pause
    exit /b 1
)

if not exist vendor (
    echo WARNING: Dependencies not installed!
    echo Please run setup first: setup.bat
    echo.
    pause
    exit /b 1
)

echo Starting Laravel development server on port 8001...
echo.
echo Access your application at:
echo   Frontend: http://localhost:8001
echo   Admin Panel: http://localhost:8001/admin
echo.
echo Admin Credentials:
echo   Email: <EMAIL>
echo   Password: password
echo.
echo Press Ctrl+C to stop the server
echo.

php artisan serve --port=8001
