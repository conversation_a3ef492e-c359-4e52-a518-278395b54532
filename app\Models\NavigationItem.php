<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NavigationItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'url',
        'type',
        'reference_id',
        'target',
        'icon',
        'is_active',
        'sort_order',
        'location',
        'parent_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'reference_id' => 'integer',
        'parent_id' => 'integer',
    ];

    /**
     * Get the parent navigation item.
     */
    public function parent()
    {
        return $this->belongsTo(NavigationItem::class, 'parent_id');
    }

    /**
     * Get the child navigation items.
     */
    public function children()
    {
        return $this->hasMany(NavigationItem::class, 'parent_id')
                    ->where('is_active', true)
                    ->orderBy('sort_order');
    }

    /**
     * Get the referenced category (if type is category).
     */
    public function category()
    {
        return $this->belongsTo(Category::class, 'reference_id');
    }

    /**
     * Get the referenced tag (if type is tag).
     */
    public function tag()
    {
        return $this->belongsTo(Tag::class, 'reference_id');
    }

    /**
     * Scope a query to only include active items.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by location.
     */
    public function scopeLocation($query, $location)
    {
        return $query->where('location', $location);
    }

    /**
     * Scope a query to only include top-level items.
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('title');
    }

    /**
     * Get the computed URL for this navigation item.
     */
    public function getComputedUrlAttribute()
    {
        if ($this->type === 'category' && $this->category) {
            return route('category.show', $this->category->slug);
        }
        
        if ($this->type === 'tag' && $this->tag) {
            return route('tag.show', $this->tag->slug);
        }
        
        return $this->url;
    }

    /**
     * Get the computed title for this navigation item.
     */
    public function getComputedTitleAttribute()
    {
        if ($this->type === 'category' && $this->category) {
            return $this->category->name;
        }
        
        if ($this->type === 'tag' && $this->tag) {
            return $this->tag->name;
        }
        
        return $this->title;
    }

    /**
     * Check if this navigation item has children.
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    /**
     * Check if this navigation item is active for the current URL.
     */
    public function isActiveForUrl(string $currentUrl): bool
    {
        $itemUrl = $this->computed_url;
        
        // Exact match
        if ($itemUrl === $currentUrl) {
            return true;
        }
        
        // Check if current URL starts with item URL (for parent items)
        if ($itemUrl !== '/' && str_starts_with($currentUrl, $itemUrl)) {
            return true;
        }
        
        // Check children
        foreach ($this->children as $child) {
            if ($child->isActiveForUrl($currentUrl)) {
                return true;
            }
        }
        
        return false;
    }
}
