@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        font-family: 'Figtree', system-ui, sans-serif;
    }
}

@layer components {
    .game-frame {
        @apply rounded-lg shadow-lg overflow-hidden bg-white;
    }
    
    .play-button {
        @apply bg-game-orange-500 hover:bg-game-orange-600 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200;
    }
    
    .loading-spinner {
        @apply animate-spin-slow w-8 h-8 border-4 border-gray-300 border-t-game-orange-500 rounded-full;
    }
    
    .nav-item {
        @apply text-gray-700 hover:text-game-orange-500 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
    }
    
    .nav-item.active {
        @apply text-game-orange-500 bg-game-orange-50;
    }
    
    .game-card {
        @apply bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200;
    }
    
    .game-card img {
        @apply w-full h-32 object-cover;
    }
    
    .game-card-content {
        @apply p-4;
    }
    
    .game-title {
        @apply font-semibold text-gray-900 mb-2 line-clamp-2;
    }
    
    .game-description {
        @apply text-sm text-gray-600 line-clamp-3;
    }
    
    .tag {
        @apply inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full mr-1 mb-1;
    }
    
    .section-title {
        @apply text-2xl font-bold text-gray-900 mb-6;
    }
    
    .search-input {
        @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-game-orange-500 focus:border-transparent;
    }
}

@layer utilities {
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}
