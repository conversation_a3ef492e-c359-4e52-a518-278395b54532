<?php

namespace App\Filament\Widgets;

use App\Models\Game;
use App\Models\Category;
use App\Models\Tag;
use App\Models\GamePlay;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $totalGames = Game::count();
        $activeGames = Game::where('is_active', true)->count();
        $totalPlays = GamePlay::count();
        $todayPlays = GamePlay::whereDate('played_at', today())->count();
        $totalCategories = Category::count();
        $totalTags = Tag::count();

        return [
            Stat::make('Total Games', $totalGames)
                ->description($activeGames . ' active games')
                ->descriptionIcon('heroicon-m-puzzle-piece')
                ->color('success'),

            Stat::make('Total Plays', number_format($totalPlays))
                ->description($todayPlays . ' plays today')
                ->descriptionIcon('heroicon-m-play')
                ->color('info'),

            Stat::make('Categories', $totalCategories)
                ->description('Game categories')
                ->descriptionIcon('heroicon-m-folder')
                ->color('warning'),

            Stat::make('Tags', $totalTags)
                ->description('Available tags')
                ->descriptionIcon('heroicon-m-tag')
                ->color('primary'),
        ];
    }
}
